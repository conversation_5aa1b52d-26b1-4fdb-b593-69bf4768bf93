import 'dart:developer' as developer;
import 'dart:io';
import 'package:http/http.dart' as http;
import '../services/api_service.dart';
import '../services/config_service.dart';
import 'app_logger.dart';

/// Network diagnostics utility for troubleshooting connectivity issues
class NetworkDiagnostics {
  static NetworkDiagnostics? _instance;
  static NetworkDiagnostics get instance => _instance ??= NetworkDiagnostics._();

  NetworkDiagnostics._();

  /// Run comprehensive network diagnostics
  Future<Map<String, dynamic>> runDiagnostics() async {
    developer.log('Running network diagnostics...');
    AppLogger.info('Running network diagnostics...');

    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'tests': <String, dynamic>{},
    };

    // Test 1: Basic connectivity
    results['tests']['basic_connectivity'] = await _testBasicConnectivity();

    // Test 2: DNS resolution
    results['tests']['dns_resolution'] = await _testDnsResolution();

    // Test 3: API endpoint connectivity
    results['tests']['api_connectivity'] = await _testApiConnectivity();

    // Test 4: Network interface info
    results['tests']['network_info'] = await _getNetworkInfo();

    // Overall status
    final allPassed = (results['tests'] as Map<String, dynamic>).values
        .every((test) => test['status'] == 'success');
    results['overall_status'] = allPassed ? 'healthy' : 'issues_detected';

    developer.log('Network diagnostics completed: ${results['overall_status']}');
    AppLogger.info('Network diagnostics completed', error: results);

    return results;
  }

  /// Test basic internet connectivity
  Future<Map<String, dynamic>> _testBasicConnectivity() async {
    try {
      final response = await http.get(
        Uri.parse('https://www.google.com'),
        headers: {'Connection': 'close'},
      ).timeout(const Duration(seconds: 10));

      return {
        'status': 'success',
        'message': 'Basic internet connectivity working',
        'statusCode': response.statusCode,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'message': 'Basic internet connectivity failed',
        'error': e.toString(),
      };
    }
  }

  /// Test DNS resolution for AWS endpoint
  Future<Map<String, dynamic>> _testDnsResolution() async {
    try {
      final serverUrl = await ConfigService.instance.getServerUrl();
      final uri = Uri.parse(serverUrl);
      final hostname = uri.host;

      // Try to resolve the hostname
      final addresses = await InternetAddress.lookup(hostname);
      
      return {
        'status': 'success',
        'message': 'DNS resolution successful',
        'hostname': hostname,
        'addresses': addresses.map((addr) => addr.address).toList(),
        'addressCount': addresses.length,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'message': 'DNS resolution failed',
        'error': e.toString(),
        'errorType': e.runtimeType.toString(),
      };
    }
  }

  /// Test API endpoint connectivity
  Future<Map<String, dynamic>> _testApiConnectivity() async {
    try {
      final result = await ApiService.instance.testConnectivity();
      return {
        'status': result['status'] == 'connected' || result['status'] == 'error' 
          ? 'success' 
          : 'failed',
        'message': result['message'],
        'details': result,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'message': 'API connectivity test failed',
        'error': e.toString(),
      };
    }
  }

  /// Get network interface information
  Future<Map<String, dynamic>> _getNetworkInfo() async {
    try {
      final interfaces = await NetworkInterface.list();
      final activeInterfaces = interfaces.where((interface) => 
        interface.addresses.isNotEmpty).toList();

      return {
        'status': 'success',
        'message': 'Network interface info retrieved',
        'interfaceCount': interfaces.length,
        'activeInterfaceCount': activeInterfaces.length,
        'interfaces': activeInterfaces.map((interface) => {
          'name': interface.name,
          'type': interface.type.name,
          'addresses': interface.addresses.map((addr) => {
            'address': addr.address,
            'type': addr.type.name,
          }).toList(),
        }).toList(),
      };
    } catch (e) {
      return {
        'status': 'failed',
        'message': 'Failed to get network interface info',
        'error': e.toString(),
      };
    }
  }

  /// Get a human-readable summary of diagnostics results
  String getSummary(Map<String, dynamic> results) {
    final buffer = StringBuffer();
    buffer.writeln('Network Diagnostics Summary');
    buffer.writeln('=' * 30);
    buffer.writeln('Overall Status: ${results['overall_status']}');
    buffer.writeln('Timestamp: ${results['timestamp']}');
    buffer.writeln();

    final tests = results['tests'] as Map<String, dynamic>;
    
    tests.forEach((testName, testResult) {
      final status = testResult['status'];
      final message = testResult['message'];
      final icon = status == 'success' ? '✅' : '❌';
      
      buffer.writeln('$icon ${testName.replaceAll('_', ' ').toUpperCase()}');
      buffer.writeln('   $message');
      
      if (status == 'failed' && testResult['error'] != null) {
        buffer.writeln('   Error: ${testResult['error']}');
      }
      
      buffer.writeln();
    });

    return buffer.toString();
  }

  /// Get troubleshooting recommendations based on diagnostics
  List<String> getRecommendations(Map<String, dynamic> results) {
    final recommendations = <String>[];
    final tests = results['tests'] as Map<String, dynamic>;

    // Check basic connectivity
    if (tests['basic_connectivity']?['status'] == 'failed') {
      recommendations.addAll([
        'Check your internet connection',
        'Try switching between WiFi and mobile data',
        'Restart your network connection',
      ]);
    }

    // Check DNS resolution
    if (tests['dns_resolution']?['status'] == 'failed') {
      recommendations.addAll([
        'DNS resolution failed - try switching DNS servers',
        'Consider using Google DNS (8.8.8.8, 8.8.4.4) or Cloudflare DNS (1.1.1.1)',
        'Clear your device\'s DNS cache',
        'Try connecting to a different network',
      ]);
    }

    // Check API connectivity
    if (tests['api_connectivity']?['status'] == 'failed') {
      recommendations.addAll([
        'API endpoint is unreachable',
        'Check if you\'re behind a corporate firewall',
        'Try using a VPN if on a restricted network',
        'Contact support if the issue persists',
      ]);
    }

    // General recommendations if no specific issues found
    if (recommendations.isEmpty && results['overall_status'] != 'healthy') {
      recommendations.addAll([
        'Try restarting the app',
        'Check for app updates',
        'Clear app cache and data',
      ]);
    }

    return recommendations;
  }
}
